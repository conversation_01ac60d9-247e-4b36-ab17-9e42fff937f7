import { useState, useCallback } from 'react';

interface CalculatorState {
  display: string;
  previousValue: number | null;
  operation: string | null;
  waitingForNewValue: boolean;
  hasDecimal: boolean;
}

export const useCalculator = (onValueChange: (value: string) => void) => {
  const [state, setState] = useState<CalculatorState>({
    display: '',
    previousValue: null,
    operation: null,
    waitingForNewValue: false,
    hasDecimal: false,
  });

  const formatNumber = (num: number): string => {
    // Türkiye formatında sayı formatla (nokta binlik ayırıcı, virgül ondalık)
    const parts = num.toString().split('.');
    const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, '.');
    const decimalPart = parts[1] ? ',' + parts[1] : '';
    return integerPart + decimalPart;
  };

  const parseDisplayValue = (display: string): number => {
    // Türkiye formatından sayıya çevir
    return parseFloat(display.replace(/\./g, '').replace(',', '.')) || 0;
  };

  const updateDisplay = useCallback((newDisplay: string) => {
    setState(prev => ({ ...prev, display: newDisplay }));
    onValueChange(newDisplay);
  }, [onValueChange]);

  const handleNumber = useCallback((num: string) => {
    setState(prev => {
      if (prev.waitingForNewValue) {
        const newDisplay = num;
        onValueChange(newDisplay);
        return {
          ...prev,
          display: newDisplay,
          waitingForNewValue: false,
          hasDecimal: false,
        };
      }

      const newDisplay = prev.display === '' ? num : prev.display + num;
      onValueChange(newDisplay);
      return {
        ...prev,
        display: newDisplay,
      };
    });
  }, [onValueChange]);

  const handleDecimal = useCallback(() => {
    setState(prev => {
      if (prev.hasDecimal) return prev;

      if (prev.waitingForNewValue) {
        const newDisplay = '0,';
        onValueChange(newDisplay);
        return {
          ...prev,
          display: newDisplay,
          waitingForNewValue: false,
          hasDecimal: true,
        };
      }

      const newDisplay = prev.display === '' ? '0,' : prev.display + ',';
      onValueChange(newDisplay);
      return {
        ...prev,
        display: newDisplay,
        hasDecimal: true,
      };
    });
  }, [onValueChange]);

  const handleOperator = useCallback((operator: string) => {
    setState(prev => {
      const currentValue = parseDisplayValue(prev.display);

      if (operator === '±') {
        const newValue = currentValue * -1;
        const newDisplay = formatNumber(newValue);
        onValueChange(newDisplay);
        return {
          ...prev,
          display: newDisplay,
        };
      }

      if (operator === '%') {
        const newValue = currentValue / 100;
        const newDisplay = formatNumber(newValue);
        onValueChange(newDisplay);
        return {
          ...prev,
          display: newDisplay,
        };
      }

      if (prev.previousValue !== null && prev.operation && !prev.waitingForNewValue) {
        const result = calculate(prev.previousValue, currentValue, prev.operation);
        const newDisplay = formatNumber(result);
        onValueChange(newDisplay);
        return {
          ...prev,
          display: newDisplay,
          previousValue: result,
          operation: operator,
          waitingForNewValue: true,
          hasDecimal: false,
        };
      }

      return {
        ...prev,
        previousValue: currentValue,
        operation: operator,
        waitingForNewValue: true,
        hasDecimal: false,
      };
    });
  }, [onValueChange]);

  const calculate = (prev: number, current: number, operation: string): number => {
    switch (operation) {
      case '+':
        return prev + current;
      case '−':
        return prev - current;
      case '×':
        return prev * current;
      case '÷':
        return current !== 0 ? prev / current : prev;
      default:
        return current;
    }
  };

  const handleEquals = useCallback(() => {
    setState(prev => {
      if (prev.previousValue === null || prev.operation === null) {
        return prev;
      }

      const currentValue = parseDisplayValue(prev.display);
      const result = calculate(prev.previousValue, currentValue, prev.operation);
      const newDisplay = formatNumber(result);
      onValueChange(newDisplay);

      return {
        display: newDisplay,
        previousValue: null,
        operation: null,
        waitingForNewValue: true,
        hasDecimal: false,
      };
    });
  }, [onValueChange]);

  const handleClear = useCallback(() => {
    const newDisplay = '';
    setState({
      display: newDisplay,
      previousValue: null,
      operation: null,
      waitingForNewValue: false,
      hasDecimal: false,
    });
    onValueChange(newDisplay);
  }, [onValueChange]);

  const handleBackspace = useCallback(() => {
    setState(prev => {
      if (prev.display.length <= 1) {
        const newDisplay = '';
        onValueChange(newDisplay);
        return {
          ...prev,
          display: newDisplay,
          hasDecimal: false,
        };
      }

      const newDisplay = prev.display.slice(0, -1);
      const hasDecimal = newDisplay.includes(',');
      onValueChange(newDisplay);

      return {
        ...prev,
        display: newDisplay,
        hasDecimal,
      };
    });
  }, [onValueChange]);

  return {
    display: state.display,
    handleNumber,
    handleOperator,
    handleEquals,
    handleDecimal,
    handleClear,
    handleBackspace,
  };
};
